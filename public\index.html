<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>预约会议</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>欢迎使用预约会议系统</h1>
        <p>这是一个使用Intercom集成的会议预约系统示例。</p>
        <p>您可以通过Intercom聊天窗口选择会议日期并预约。</p>
    </div>
    
    <!-- Intercom集成 -->
    <script>
        //Set your APP_ID
        var APP_ID = "994b4132-b2c8-455c-a3dd-6673d0570eb6";
        var current_user_email = "<EMAIL>";
        var current_user_name = "<PERSON> Bloggs";
        var current_user_id = "123456378";

        window.intercomSettings = {
            app_id: APP_ID,
            name: current_user_name,
            email: current_user_email,
            user_id: current_user_id,
        };
        (function () {
            var w = window;
            var ic = w.Intercom;
            if (typeof ic === "function") {
                ic("update", w.intercomSettings);
            } else {
                var d = document;
                var i = function () {
                    i.c(arguments);
                };
                i.q = [];
                i.c = function (args) {
                    i.q.push(args);
                };
                w.Intercom = i;
                var l = function () {
                    var s = d.createElement("script");
                    s.type = "text/javascript";
                    s.async = true;
                    s.src = "https://widget.intercom.io/widget/" + APP_ID;
                    var x = d.getElementsByTagName("script")[0];
                    x.parentNode.insertBefore(s, x);
                };
                if (document.readyState === "complete") {
                    l();
                } else if (w.attachEvent) {
                    w.attachEvent("onload", l);
                } else {
                    w.addEventListener("load", l, false);
                }
            }
        })();
    </script>
</body>
</html> 
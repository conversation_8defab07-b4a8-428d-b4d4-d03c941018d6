services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8891:8891"
    environment:
      - NODE_ENV=production
      - PORT=8891
      # 数据库配置环境变量
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=chat
      - DB_USER=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_CHARSET=utf8mb4
      - DB_TIMEZONE=Asia/Shanghai
      # Coze 配置通过.env自动传递
      - COZE_API_TOKEN=${COZE_API_TOKEN}
      - COZE_BOT_ID=${COZE_BOT_ID}
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    # 依赖关系确保MySQL先启动
    depends_on:
      mysql:
        condition: service_healthy
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:8891"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    networks:
      - chat-network
      
  # MySQL数据库服务
  mysql:
    build:
      context: ./mysql
      dockerfile: Dockerfile
    command: 
      - --default-authentication-plugin=mysql_native_password
      - --bind-address=0.0.0.0
      - --disabled_storage_engines=FEDERATED
      - --require_secure_transport=OFF
      - --skip-ssl
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=chat
      - MYSQL_ROOT_HOST=%  # 允许任何主机连接
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./db/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - chat-network
      
  # phpMyAdmin管理工具(可选)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    restart: unless-stopped
    ports:
      - "8892:80"
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - PMA_ABSOLUTE_URI=https://test124.abupdate.com/mysql_client/ # 确保 phpMyAdmin 知道其公开的 HTTPS URL
    volumes:
      - ./phpmyadmin-servername.conf:/etc/apache2/conf-enabled/phpmyadmin-servername.conf
    depends_on:
      - mysql
    networks:
      - chat-network

volumes:
  mysql-data:

networks:
  chat-network:
    driver: bridge 
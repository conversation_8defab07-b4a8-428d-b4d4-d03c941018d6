// 官方 coze-js SDK 用法
import { CozeAPI } from '@coze/api';
import dotenv from 'dotenv';
import { UserConversation } from './db/models/UserConversation.js';
import { closePool } from './db/connection.js';
import { appendFileSync } from 'fs';
import util from 'util';
import { UserProfiles } from './db/models/UserProfiles.js';
import { SystemConfigs } from './db/models/SystemConfigs.js';
dotenv.config();

const COZE_TOKEN = await SystemConfigs.getConfig('COZE_API_TOKEN');
const BOT_ID = await SystemConfigs.getConfig('COZE_BOT_ID');

const apiClient = new CozeAPI({
  token: COZE_TOKEN,
  baseURL: 'https://api.coze.cn'
});

/**
 * 获取用户的会话信息
 * @param {string} userId 用户ID
 * @returns {Promise<{conversationId: string|null, chatId: string|null}>} 会话信息
 */
async function getUserConversationInfo(userId) {
  try {
    // 查询数据库，获取用户的会话ID和聊天ID
    const info = await UserConversation.getConversationInfo(userId);
    if (info) {
      console.log(`用户 ${userId} 查询结果: 会话ID=${info.conversationId}, 聊天ID=${info.chatId || '无'}`);
      return info;
    } else {
      console.log(`用户 ${userId} 未找到会话信息`);
      return { conversationId: null, chatId: null };
    }
  } catch (error) {
    console.error(`获取用户 ${userId} 会话信息出错:`, error);
    throw error;
  }
}

/**
 * 获取用户的会话ID
 * @param {string} userId 用户ID
 * @returns {Promise<string|null>} 会话ID
 */
async function getUserConversationId(userId) {
  try {
    // 查询数据库，获取用户的会话ID
    const conversationId = await UserConversation.getConversationId(userId);
    console.log(`用户 ${userId} 查询会话结果:`, conversationId ? `找到会话ID ${conversationId}` : '未找到会话ID');
    return conversationId; // 如果不存在，返回null
  } catch (error) {
    console.error(`获取用户 ${userId} 会话ID出错:`, error);
    throw error;
  }
}

/**
 * 保存用户会话映射
 * @param {string} userId 用户ID
 * @param {string} conversationId 会话ID
 * @param {string|null} chatId 聊天ID
 * @returns {Promise<void>}
 */
async function saveUserConversation(userId, conversationId, chatId = null) {
  try {
    console.log(`正在保存用户 ${userId} 的会话信息:`, 
      `会话ID=${conversationId}`, 
      chatId ? `, 聊天ID=${chatId}` : '');
    await UserConversation.saveConversation(userId, conversationId, chatId);
    console.log(`用户 ${userId} 的会话信息保存成功`);
  } catch (error) {
    console.error(`保存用户 ${userId} 会话信息出错:`, error);
    throw error;
  }
}

/**
 * 更新用户的聊天ID
 * @param {string} userId 用户ID
 * @param {string} chatId 聊天ID
 * @returns {Promise<void>}
 */
async function updateUserChatId(userId, chatId) {
  try {
    console.log(`正在更新用户 ${userId} 的聊天ID: ${chatId}`);
    await UserConversation.updateChatId(userId, chatId);
    console.log(`用户 ${userId} 的聊天ID更新成功`);
  } catch (error) {
    console.error(`更新用户 ${userId} 聊天ID出错:`, error);
    throw error;
  }
}

/**
 * 使用 Coze SDK 进行对话（v3/chat，支持上下文与流式响应）
 * @param {string} userId 用户唯一标识
 * @param {string} message 用户输入内容
 * @returns {Promise<{conversationId: string, chatId: string}>} 会话ID和聊天ID
 */
async function cozeChat(userId, message) {
  // 先查询用户是否有会话信息
  const { conversationId, chatId } = await getUserConversationInfo(userId);
  
  // 准备对话请求参数
  const chatParams = {
    bot_id: BOT_ID,
    user_id: userId,
    stream: true,
    auto_save_history: true,
    additional_messages: [
      {
        role: "user",
        content: message,
        content_type: "text"
      }
    ]
  };
  
  // 如果有会话ID，则添加到请求参数中
  if (conversationId) {
    chatParams.conversation_id = conversationId;
    console.log(`使用已有会话ID进行对话: ${conversationId}`);
  } else {
    console.log(`首次对话，不传入会话ID，等待服务端生成`);
  }
  
  // 发起对话请求
  const res = await apiClient.chat.stream(chatParams);
  
  // 从响应中获取会话ID和聊天ID
  let foundConversationId = null;
  let foundChatId = null;
  
  for await (const chunk of res) {
    console.log('原始 Coze SDK chunk:', JSON.stringify(chunk, null, 2));
    console.log('Coze 回复:', chunk);
    
    // 检查是否包含会话ID信息
    if (!foundConversationId && 
        chunk.event === 'conversation.chat.created' && 
        chunk.data?.conversation_id) {
      foundConversationId = chunk.data.conversation_id;
      foundChatId = chunk.data.id; // 聊天ID
      console.log(`从响应中获取到会话信息: 会话ID=${foundConversationId}, 聊天ID=${foundChatId}`);
      
      // 保存或更新会话信息
      if (!conversationId) {
        // 新用户，保存会话ID和聊天ID
        await saveUserConversation(userId, foundConversationId, foundChatId);
      } else if (!chatId || chatId !== foundChatId) {
        // 老用户，但有新的聊天ID，更新聊天ID
        await updateUserChatId(userId, foundChatId);
      }
    }
    
    // 如果在其他事件中找到了聊天ID
    if (!foundChatId && chunk.data?.chat_id) {
      foundChatId = chunk.data.chat_id;
      console.log(`从响应中获取到聊天ID: ${foundChatId}`);
      
      // 如果已有会话ID但没有聊天ID，则更新聊天ID
      if (conversationId && (!chatId || chatId !== foundChatId)) {
        await updateUserChatId(userId, foundChatId);
      }
    }
  }
  
  return { 
    conversationId: foundConversationId || conversationId,
    chatId: foundChatId || chatId
  };
}

export async function* cozeChatStream(userId, message) {
  // 先查询用户是否有会话信息
  const { conversationId, chatId } = await getUserConversationInfo(userId);
  
  // 准备对话请求参数
  const chatParams = {
    bot_id: BOT_ID,
    user_id: userId,
    stream: true,
    auto_save_history: true,
    additional_messages: [
      {
        role: "user",
        content: message,
        content_type: "text"
      }
    ]
  };
  
  // 如果有会话ID，则添加到请求参数中
  if (conversationId) {
    chatParams.conversation_id = conversationId;
    console.log(`使用已有会话ID进行流式对话: ${conversationId}`);
  } else {
    console.log(`首次流式对话，不传入会话ID，等待服务端生成`);
  }
  
  // 发起对话请求
  const res = await apiClient.chat.stream(chatParams);
  
  // 从响应中获取会话ID和聊天ID
  let foundConversationId = null;
  let foundChatId = null;

  for await (const chunk of res) {
    console.log('原始 Coze SDK chunk:', JSON.stringify(chunk, null, 2));
    // 检查是否包含会话ID和聊天ID信息
    if (!foundConversationId && 
        chunk.event === 'conversation.chat.created' && 
        chunk.data?.conversation_id) {
      foundConversationId = chunk.data.conversation_id;
      foundChatId = chunk.data.id; // 聊天ID
      console.log(`从流式响应中获取到会话信息: 会话ID=${foundConversationId}, 聊天ID=${foundChatId}`);
      
      // 保存或更新会话信息
      if (!conversationId) {
        // 新用户，保存会话ID和聊天ID
        await saveUserConversation(userId, foundConversationId, foundChatId);
      } else if (!chatId || chatId !== foundChatId) {
        // 老用户，但有新的聊天ID，更新聊天ID
        await updateUserChatId(userId, foundChatId);
      }
    }
    
    // 如果在其他事件中找到了聊天ID
    if (!foundChatId && chunk.data?.chat_id) {
      foundChatId = chunk.data.chat_id;
      console.log(`从流式响应中获取到聊天ID: ${foundChatId}`);
      
      // 如果已有会话ID但没有聊天ID，则更新聊天ID
      if (conversationId && (!chatId || chatId !== foundChatId)) {
        await updateUserChatId(userId, foundChatId);
      }
    }
    
    // 将每个响应块传递给调用者
    yield chunk;
  }
}

/**
 * 查询对话历史记录
 * @param {string} conversationId 会话ID
 * @param {string} chatId 对话ID
 * @returns {Promise<any>} 对话历史记录
 */
async function getChatHistory(conversationId, chatId) {
  try {
    console.log(`开始获取对话历史，会话ID=${conversationId}, 对话ID=${chatId}`);
    const response = await fetch(
      `https://api.coze.cn/v3/chat/message/list?conversation_id=${conversationId}&chat_id=${chatId}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${COZE_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const data = await response.json();
    if (data.code !== 0) {
      throw new Error(`获取对话历史失败: ${data.msg}`);
    }
    
    console.log(`成功获取对话历史，共 ${data.data?.length || 0} 条消息`);
    return data;
  } catch (error) {
    console.error('获取对话历史出错:', error);
    throw error;
  }
}

/**
 * 根据用户ID获取对话历史
 * 这是一个公开的API函数，供外部调用
 * @param {string} userId 用户ID
 * @returns {Promise<{success: boolean, data?: any, error?: string}>} 对话历史或错误信息
 */
export async function getHistoryByUserId(userId) {
  try {
    console.log(`开始获取用户 ${userId} 的对话历史...`);
    
    // 步骤1: 获取用户的会话信息
    const { conversationId, chatId } = await getUserConversationInfo(userId);
    
    // 如果用户没有会话记录，返回空结果
    if (!conversationId || !chatId) {
      console.log(`用户 ${userId} 没有可查询的对话历史`);
      return { 
        success: true, 
        data: { 
          messages: [],
          user_id: userId,
          conversation_id: null,
          chat_id: null
        }
      };
    }
    
    // 步骤2: 使用会话ID和对话ID获取历史记录
    console.log(`查询用户 ${userId} 的对话历史，会话ID=${conversationId}, 对话ID=${chatId}`);
    const historyResponse = await getChatHistory(conversationId, chatId);
    
    // 步骤3: 处理和重组历史记录
    let resultMessages = [];
    
    if (historyResponse && historyResponse.data && Array.isArray(historyResponse.data)) {
      console.log(`原始消息数量: ${historyResponse.data.length}`);
      
      // 按chat_id分组消息，每个chat_id是一组对话
      const messageGroups = {};
      
      historyResponse.data.forEach(message => {
        const chatId = message.chat_id;
        if (!messageGroups[chatId]) {
          messageGroups[chatId] = [];
        }
        messageGroups[chatId].push(message);
      });
      
      // 处理每组消息
      Object.values(messageGroups).forEach(group => {
        // 用于检测重复内容
        const contentSet = new Set();
        
        // 提取这组消息中的function_call、answer和普通消息
        const functionCalls = group.filter(msg => msg.type === 'function_call');
        const answers = group.filter(msg => msg.type === 'answer');
        const userMessages = group.filter(msg => msg.role === 'user');
        
        // 提取用户问题
        let userQuestion = null;
        
        if (functionCalls.length > 0) {
          // 从function_call中提取用户问题
          const functionCall = functionCalls[0]; // 取第一个
          try {
            const functionData = JSON.parse(functionCall.content);
            let userInput = null;

            if (functionData.arguments) {
              if (typeof functionData.arguments.input === 'string') {
                userInput = functionData.arguments.input;
              } else if (Array.isArray(functionData.arguments.data) &&
                         functionData.arguments.data.length > 0 &&
                         functionData.arguments.data[0] &&
                         typeof functionData.arguments.data[0].value === 'string') {
                userInput = functionData.arguments.data[0].value;
              }
            }

            if (userInput !== null) {
              // 创建一个合成的用户消息，确保它排在最前面
              userQuestion = {
                id: `user_${functionCall.id}`,
                role: 'user',
                content: userInput,
                content_type: 'text',
                conversation_id: functionCall.conversation_id,
                chat_id: functionCall.chat_id,
                created_at: functionCall.created_at - 1, // 确保时间戳比其他消息早
                type: 'user_question'
              };
            }
          } catch (e) {
            console.error('解析function_call消息失败:', e);
          }
        }
        
        // 如果有真实的用户消息，优先使用它们
        if (userMessages.length > 0) {
          userMessages.forEach(msg => resultMessages.push(msg));
        } 
        // 否则使用从function_call提取的用户问题
        else if (userQuestion) {
          resultMessages.push(userQuestion);
        }
        
        // 添加answer消息，去重
        answers.forEach(answer => {
          if (answer.content && typeof answer.content === 'string') {
            const cleanContent = answer.content.trim();
            if (!contentSet.has(cleanContent)) {
              contentSet.add(cleanContent);
              resultMessages.push(answer);
            } else {
              console.log(`过滤掉重复answer消息, ID: ${answer.id}`);
            }
          } else {
            resultMessages.push(answer);
          }
        });
      });
      
      // 按时间戳排序
      resultMessages.sort((a, b) => a.created_at - b.created_at);
      
      console.log(`处理后的消息数量: ${resultMessages.length}/${historyResponse.data.length}`);
    } else {
      console.log('历史记录数据为空或格式不正确');
    }
    
    // 步骤4: 返回处理后的结果
    return {
      success: true,
      data: {
        messages: resultMessages,
        user_id: userId,
        conversation_id: conversationId,
        chat_id: chatId
      }
    };
  } catch (error) {
    console.error(`获取用户 ${userId} 对话历史失败:`, error);
    return {
      success: false,
      error: `获取对话历史失败: ${error.message || '未知错误'}`
    };
  }
}

/**
 * 查询用户对话历史记录
 * 内部工具函数，不导出
 * @param {string} userId 用户ID
 * @returns {Promise<any>} 对话历史记录
 */
async function getUserChatHistory(userId) {
  try {
    // 获取用户的会话信息
    const { conversationId, chatId } = await getUserConversationInfo(userId);
    
    if (!conversationId || !chatId) {
      console.log(`用户 ${userId} 没有可查询的历史记录`);
      return null;
    }
    
    // 查询历史记录
    return await getChatHistory(conversationId, chatId);
  } catch (error) {
    console.error(`获取用户 ${userId} 历史记录出错:`, error);
    throw error;
  }
}


async function saveConversationDataToDB(data) {
  console.log('[LOG] 正在尝试保存会话数据到数据库:', data);
  // 示例：这里可以插入或更新数据库记录
  // const { userId, conversationId, chatId, contactInfo, companyName, businessDetails } = data;
  // try {
  //   await db('conversations_data').insert({
  //     user_id: userId,
  //     conversation_id: conversationId,
  //     chat_id: chatId,
  //     contact_info: contactInfo,
  //     company_name: companyName,
  //     business_details: businessDetails,
  //     updated_at: new Date()
  //   }).onConflict('conversation_id') // 假设conversation_id是唯一标识符，可用于更新
  //     .merge();
  //   console.log('[LOG] 会话数据保存成功');
  // } catch (error) {
  //   console.error('[ERROR] 保存会话数据失败:', error);
  // }
}

const argumentsMap = [
  {function_name: 'updateCompanyName', key: '企业名称', value: 'company_name'},
  {function_name: 'updateContactInfo', key: '联系方式', value: 'contact_info'},
  {function_name: 'updateBusinessDetails', key:'具体业务', value: 'business_details'}
]

/**
 * 处理Coze流式响应，过滤特定类型的消息
 * @param {Object} chunk Coze响应块
 * @returns {Object|null} 处理后的消息，或null表示应该跳过该消息
 */
function processCozeResponse(userId , chunk) {
  // 如果是'done'事件或对话状态相关事件，直接返回
  if (chunk.event === 'done' || 
      chunk.event === 'conversation.chat.in_progress' || 
      chunk.event === 'conversation.chat.completed' ||
      chunk.event === 'conversation.chat.created') {
    return chunk;
  }
  
  // 检查是否为消息事件
  if (chunk.event.startsWith('conversation.message.')) {
    // 跳过tool_response和function_call类型的消息
    if (chunk.data?.type === 'tool_response' || chunk.data?.type === 'function_call') {

      if(chunk.data?.type === 'function_call' && chunk.data?.content.includes('ts-keyword_memory-setKeywordMemory')) {
         const content = JSON.parse(chunk.data?.content)
         console.log(`content: ${JSON.stringify(content)}`);
         if(content.arguments?.data[0]?.value) {
           const keyword = content.arguments.data[0].keyword;
           const value = content.arguments.data[0].value;
           const functionData = argumentsMap.find(item => item.key === keyword);
           console.log(`userId: ${userId}`);
           console.log(`提取到关键字: ${keyword}`);
           console.log(`提取到企业名称: ${value}`);
           UserProfiles[functionData.function_name](userId, value);
         }
      }

      console.log(`跳过消息类型: ${chunk.data.type}`);
      return null;
    }
    
    // 跳过verbose类型消息
    if (chunk.data?.type === 'verbose') {
      console.log('跳过verbose类型消息');
      return null;
    }
    
    // 只保留answer类型的实际内容，并且只处理完整消息或增量消息
    if (chunk.data?.type === 'answer') {
      // 对于增量消息，只返回内容部分
      if (chunk.event === 'conversation.message.delta') {
        return {
          event: 'message.delta',
          data: {
            content: chunk.data.content,
            content_type: chunk.data.content_type
          }
        };
      }
      
      // 对于完整消息，返回完整内容
      if (chunk.event === 'conversation.message.completed') {
        return {
          event: 'message.completed',
          data: {
            content: chunk.data.content,
            content_type: chunk.data.content_type
          }
        };
      }
    }
  }
  
  // 所有不符合条件的消息都被过滤掉
  return null;
}

/**
 * 优化的流式响应函数，过滤不必要的消息类型
 * 只返回实际需要显示的内容
 * @param {string} userId 用户ID
 * @param {string} message 消息内容
 */
export async function* filteredCozeChatStream(userId, message) {
  // 调用原始的流式响应函数
  const streamGenerator = cozeChatStream(userId, message);
  
  // 用于跟踪最终回复内容
  let finalContent = '';
  let hasYieldedFinalContent = false;
  
  // 处理并过滤响应
  for await (const chunk of streamGenerator) {
    // 处理响应块，过滤无需显示的内容
    const processedChunk = processCozeResponse(userId, chunk);
    
    // 如果处理后的结果不为空
    if (processedChunk) {
      // 如果是增量消息，更新最终内容
      if (processedChunk.event === 'message.delta' && processedChunk.data?.content) {
        // 只传递内容增量
        yield {
          event: 'content.update',
          data: {
            content: processedChunk.data.content,
            content_type: processedChunk.data.content_type || 'text'
          }
        };
      } 
      // 如果是最终完整消息，保存并传递最终内容
      else if (processedChunk.event === 'message.completed' && processedChunk.data?.content) {
        finalContent = processedChunk.data.content;
        
        // 只传递一次最终内容
        if (!hasYieldedFinalContent) {
          hasYieldedFinalContent = true;
          yield {
            event: 'content.final',
            data: {
              content: finalContent,
              content_type: processedChunk.data.content_type || 'text'
            }
          };
        }
      }
      // 传递对话状态相关事件
      else if (processedChunk.event === 'done' || 
               processedChunk.event === 'conversation.chat.completed' ||
               processedChunk.event === 'conversation.chat.created' ||
               processedChunk.event === 'conversation.chat.in_progress') {
        // 如果要传递状态事件，可以取消这里的注释
        // yield processedChunk;
      }
    }
  }
  
  // 确保最终消息被传递
  if (finalContent && !hasYieldedFinalContent) {
    yield {
      event: 'content.final',
      data: {
        content: finalContent,
        content_type: 'text'
      }
    };
  }
}

// // --- 以下是新增的测试用例 ---
// (async () => {
//   // --- 日志记录设置开始 ---
//   const logFile = 'coze-api-test.log';
//   const originalConsoleLog = console.log;
//   const originalConsoleWarn = console.warn;
//   const originalConsoleError = console.error;

//   const logStream = (type, ...args) => {
//     const message = util.format(...args);
//     const timestamp = new Date().toISOString();
//     appendFileSync(logFile, `[${timestamp}] [${type.toUpperCase()}] ${message}\n`);
//     // 如果仍然希望在控制台看到输出，可以取消下面这行注释
//     // originalConsoleLog(`[${type.toUpperCase()}] ${message}`); 
//   };

//   console.log = (...args) => logStream('log', ...args);
//   console.warn = (...args) => logStream('warn', ...args);
//   console.error = (...args) => logStream('error', ...args);
//   // --- 日志记录设置结束 ---

//   console.log('--- 开始测试用例 ---');
//   // 您可以在 .env 文件中设置 TEST_USER_ID，或者使用下面的默认值
//   const testUserId = process.env.TEST_USER_ID || '121321321';
//   const testMessage = '我的公司是北京科技2有限公司';

//   try {
//     console.log(`\n[测试阶段 1] 使用 filteredCozeChatStream 与用户 ${testUserId} 对话...`);
//     console.log(`发送消息: "${testMessage}"`);

//     let streamMessageCount = 0;
//     let streamedFinalContent = '';

//     for await (const chunk of filteredCozeChatStream(testUserId, testMessage)) {
//       console.log('[流式响应 chunk]:', JSON.stringify(chunk, null, 2));
//       if (chunk.event === 'content.update' && chunk.data?.content) {
//         // 增量内容，可以用于实时显示
//         // console.log(`[内容更新]: ${chunk.data.content}`); // 避免过多日志，按需打开
//         streamMessageCount++;
//       } else if (chunk.event === 'content.final' && chunk.data?.content) {
//         streamedFinalContent = chunk.data.content;
//         console.log(`[最终内容]: ${streamedFinalContent}`);
//         streamMessageCount++;
//       } else if (chunk.event === 'done') {
//         console.log('[流式响应结束事件]: done');
//       } else if (chunk.event === 'conversation.chat.created') {
//         console.log(`[对话创建事件]: Conversation ID: ${chunk.data?.conversation_id}, Chat ID: ${chunk.data?.id}`);
//       }
//     }
//     console.log(`流式对话结束。共收到 ${streamMessageCount} 个内容相关的事件。`);
//     if (!streamedFinalContent) {
//         console.warn("警告：流式响应未捕获到最终内容 (content.final)，请检查Coze服务或机器人配置。");
//     } else {
//         console.log(`捕获到的流式最终回复内容: "${streamedFinalContent.substring(0, 100)}${streamedFinalContent.length > 100 ? '...' : ''}"`);
//     }

//     // 等待一段时间，确保历史记录有机会被写入（如果Coze后端有延迟）
//     console.log('\n等待2秒，以便Coze后端处理历史记录...');
//     await new Promise(resolve => setTimeout(resolve, 2000));

//     console.log(`\n[测试阶段 2] 获取用户 ${testUserId} 的对话历史...`);
//     const historyResult = await getHistoryByUserId(testUserId);

//     if (historyResult.success && historyResult.data) {
//       console.log(`获取历史成功。用户: ${historyResult.data.user_id}, 会话ID: ${historyResult.data.conversation_id}, 聊天ID: ${historyResult.data.chat_id}`);
//       console.log(`历史消息 (共 ${historyResult.data.messages.length} 条):`);
//       historyResult.data.messages.forEach((msg, index) => {
//         const contentPreview = typeof msg.content === 'string' ? msg.content.substring(0, 70) : JSON.stringify(msg.content).substring(0, 70);
//         console.log(`  [${index + 1}] Role: ${msg.role}, Type: ${msg.type}, Content: ${contentPreview}${msg.content && (typeof msg.content === 'string' ? msg.content.length > 70 : JSON.stringify(msg.content).length > 70) ? '...' : ''}`);
//       });

//       // 验证历史记录
//       if (historyResult.data.messages.length > 0) {
//         const lastUserMessageInHistory = historyResult.data.messages
//             .filter(m => m.role === 'user' && (m.type === 'user_question' || m.content_type === 'text'))
//             .pop();
//         const lastAssistantMessageInHistory = historyResult.data.messages
//             .filter(m => m.role === 'assistant' && m.type === 'answer')
//             .pop();

//         if (lastUserMessageInHistory && lastUserMessageInHistory.content === testMessage) {
//             console.log("\n[验证通过] 历史记录中包含已发送的测试消息。");
//         } else {
//             console.warn(`[验证注意] 历史记录中用户消息验证:`);
//             console.warn(`  - 期望用户消息: "${testMessage}"`);
//             console.warn(`  - 历史中最后用户消息: "${lastUserMessageInHistory?.content}" (ID: ${lastUserMessageInHistory?.id})`);
//         }

//         if (streamedFinalContent && lastAssistantMessageInHistory && lastAssistantMessageInHistory.content.includes(streamedFinalContent.substring(0, Math.min(20, streamedFinalContent.length) ))) {
//             console.log("[验证通过] 历史记录中机器人回复与流式最终内容部分匹配。");
//         } else if (streamedFinalContent) {
//             console.warn(`[验证注意] 历史记录中机器人回复与流式最终内容匹配验证:`);
//             console.warn(`  - 流式最终内容 (前20字符): "${streamedFinalContent.substring(0, 20)}..."`);
//             console.warn(`  - 历史最后回复 (前20字符): "${lastAssistantMessageInHistory?.content?.substring(0, 20)}..." (ID: ${lastAssistantMessageInHistory?.id})`);
//             console.warn("  (注意：完全匹配可能因内容格式或截断而异，部分匹配或语义相似性更重要)");
//         } else {
//              console.warn("[验证提示] 流式响应未收到最终内容 (streamedFinalContent)，无法精确验证历史记录中的机器人回复。");
//         }
//       } else {
//         console.warn("[验证注意] 历史记录为空，无法执行消息内容验证。");
//       }

//     } else {
//       console.error('获取历史失败:', historyResult.error || '未知错误');
//     }

//   } catch (error) {
//     console.error('测试用例执行过程中发生错误:', error);
//   } finally {
//     console.log('\n--- 测试用例结束 ---');
//     try {
//       // 确保 closePool 是异步的，如果它返回 Promise
//       await closePool(); 
//       console.log('数据库连接池已尝试关闭。');
//     } catch (e) {
//       console.error('关闭数据库连接池时发生错误:', e);
//     }
//     // 确保Node.js进程在测试完成后退出，特别是在有打开的句柄（如数据库连接）时
//     // process.exit(0); // 如果需要强制退出，可以取消注释，但通常更好的做法是确保所有异步操作完成
//   }
// })();

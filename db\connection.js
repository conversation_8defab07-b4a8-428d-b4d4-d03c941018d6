import mysql from 'mysql2/promise';
import { dbConfig } from './config.js';

// 创建临时连接池（不指定数据库）
const tempPool = mysql.createPool({
  ...dbConfig,
  database: undefined
});

// 初始化数据库
async function initDatabase() {
  try {
    // 创建数据库（如果不存在）
    await tempPool.query(`
      CREATE DATABASE IF NOT EXISTS ${dbConfig.database}
      DEFAULT CHARACTER SET utf8mb4
      DEFAULT COLLATE utf8mb4_unicode_ci
    `);
    console.log(`数据库 ${dbConfig.database} 已就绪`);
  } catch (error) {
    console.error('初始化数据库失败:', error);
    throw error;
  } finally {
    await tempPool.end();
  }
}

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 最大重试次数
const MAX_RETRIES = 3;
// 重试延迟（毫秒）
const RETRY_DELAY = 1000;

/**
 * 延迟函数
 * @param {number} ms 延迟毫秒数
 * @returns {Promise<void>}
 */
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 获取数据库连接
 * @returns {Promise<mysql.PoolConnection>}
 */
export async function getConnection() {
  let retries = 0;
  while (retries < MAX_RETRIES) {
    try {
      const connection = await pool.getConnection();
      return connection;
    } catch (error) {
      retries++;
      if (retries === MAX_RETRIES) {
        throw new Error(`获取数据库连接失败: ${error.message}`);
      }
      console.warn(`获取数据库连接失败，第 ${retries} 次重试...`);
      await delay(RETRY_DELAY);
    }
  }
}

/**
 * 执行SQL查询
 * @param {string} sql SQL语句
 * @param {Array} params 参数数组
 * @returns {Promise<any>} 查询结果
 */
export async function executeQuery(sql, params = []) {
  const connection = await getConnection();
  try {
    const [results] = await connection.execute(sql, params);
    return results;
  } catch (error) {
    console.error('SQL执行错误:', error);
    throw new Error(`SQL执行错误: ${error.message}`);
  } finally {
    connection.release();
  }
}

/**
 * 执行事务
 * @param {Function} callback 事务回调函数
 * @returns {Promise<any>} 事务执行结果
 */
export async function executeTransaction(callback) {
  const connection = await getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * 关闭连接池
 * @returns {Promise<void>}
 */
export async function closePool() {
  try {
    await pool.end();
    console.log('数据库连接池已关闭');
  } catch (error) {
    console.error('关闭数据库连接池失败:', error);
    throw error;
  }
}

// 监听连接池事件
pool.on('connection', (connection) => {
  console.log('新的数据库连接已创建');
});

pool.on('error', (err) => {
  console.error('数据库连接池错误:', err);
});

// 初始化数据库
await initDatabase();

// 导出 pool
export { pool }; 
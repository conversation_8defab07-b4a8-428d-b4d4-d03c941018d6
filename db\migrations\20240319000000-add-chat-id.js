import { pool } from '../connection.js';

export async function up({ pool }) {
  try {
    console.log('准备为user_conversations表添加chat_id字段...');
    
    // 检查字段是否已存在
    const [columns] = await pool.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'chat' 
      AND TABLE_NAME = 'user_conversations' 
      AND COLUMN_NAME = 'chat_id'
    `);
    
    if (columns.length === 0) {
      // 字段不存在，添加字段
      await pool.query(`
        ALTER TABLE user_conversations 
        ADD COLUMN chat_id VARCHAR(64) DEFAULT NULL COMMENT 'Coze对话ID' AFTER conversation_id,
        ADD INDEX idx_chat_id (chat_id)
      `);
      console.log('chat_id字段添加成功');
    } else {
      console.log('chat_id字段已存在，无需添加');
    }
  } catch (err) {
    console.error('添加chat_id字段失败:', err);
    throw err;
  }
}

export async function down({ pool }) {
  try {
    console.log('准备移除user_conversations表的chat_id字段...');
    
    // 检查字段是否存在
    const [columns] = await pool.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'chat' 
      AND TABLE_NAME = 'user_conversations' 
      AND COLUMN_NAME = 'chat_id'
    `);
    
    if (columns.length > 0) {
      // 移除索引和字段
      await pool.query(`
        ALTER TABLE user_conversations 
        DROP INDEX idx_chat_id,
        DROP COLUMN chat_id
      `);
      console.log('chat_id字段移除成功');
    } else {
      console.log('chat_id字段不存在，无需移除');
    }
  } catch (err) {
    console.error('移除chat_id字段失败:', err);
    throw err;
  }
} 
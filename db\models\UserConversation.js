import { executeQuery } from '../connection.js';

export class UserConversation {
  /**
   * 根据用户ID获取会话信息
   * @param {string} userId 用户ID
   * @returns {Promise<{conversationId: string, chatId: string}|null>} 会话信息
   */
  static async getConversationInfo(userId) {
    const results = await executeQuery(
      'SELECT conversation_id, chat_id FROM user_conversations WHERE user_id = ?',
      [userId]
    );
    return results.length > 0 ? {
      conversationId: results[0].conversation_id,
      chatId: results[0].chat_id
    } : null;
  }

  /**
   * 根据用户ID获取会话ID
   * @param {string} userId 用户ID
   * @returns {Promise<string|null>} 会话ID
   */
  static async getConversationId(userId) {
    const results = await executeQuery(
      'SELECT conversation_id FROM user_conversations WHERE user_id = ?',
      [userId]
    );
    return results.length > 0 ? results[0].conversation_id : null;
  }

  /**
   * 根据用户ID获取聊天ID
   * @param {string} userId 用户ID
   * @returns {Promise<string|null>} 聊天ID
   */
  static async getChatId(userId) {
    const results = await executeQuery(
      'SELECT chat_id FROM user_conversations WHERE user_id = ?',
      [userId]
    );
    return results.length > 0 ? results[0].chat_id : null;
  }

  /**
   * 保存用户会话映射
   * @param {string} userId 用户ID
   * @param {string} conversationId 会话ID
   * @param {string} chatId 聊天ID
   * @returns {Promise<void>}
   */
  static async saveConversation(userId, conversationId, chatId = null) {
    // 检查是否已存在记录
    const existing = await executeQuery(
      'SELECT id FROM user_conversations WHERE user_id = ?',
      [userId]
    );

    if (existing.length > 0) {
      // 更新现有记录
      await executeQuery(
        'UPDATE user_conversations SET conversation_id = ?, chat_id = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
        [conversationId, chatId, userId]
      );
    } else {
      // 插入新记录
      await executeQuery(
        'INSERT INTO user_conversations (user_id, conversation_id, chat_id) VALUES (?, ?, ?)',
        [userId, conversationId, chatId]
      );
    }
  }

  /**
   * 更新用户的聊天ID
   * @param {string} userId 用户ID
   * @param {string} chatId 聊天ID
   * @returns {Promise<void>}
   */
  static async updateChatId(userId, chatId) {
    const results = await executeQuery(
      'SELECT id FROM user_conversations WHERE user_id = ?',
      [userId]
    );

    if (results.length > 0) {
      await executeQuery(
        'UPDATE user_conversations SET chat_id = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
        [chatId, userId]
      );
    }
  }

  /**
   * 创建表结构
   * @returns {Promise<void>}
   */
  static async createTable() {
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS user_conversations (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
        conversation_id VARCHAR(64) NOT NULL COMMENT 'Coze会话ID',
        chat_id VARCHAR(64) DEFAULT NULL COMMENT 'Coze对话ID',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY \`uk_user_id\` (\`user_id\`),
        INDEX \`idx_conversation_id\` (\`conversation_id\`),
        INDEX \`idx_chat_id\` (\`chat_id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户对话映射表'
    `);
  }
} 
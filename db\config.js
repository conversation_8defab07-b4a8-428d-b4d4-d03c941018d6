import dotenv from 'dotenv';

dotenv.config();

export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'KbddCpLvFdCN9H2',
  database: process.env.DB_NAME || 'chat',
  charset: process.env.DB_CHARSET || 'utf8',
  // 连接池配置
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  // 其他配置
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
  // 完全禁用SSL
  ssl: false

}; 
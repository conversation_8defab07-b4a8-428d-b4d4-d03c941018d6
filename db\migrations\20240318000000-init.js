import { pool } from '../connection.js';

export async function up({ pool }) {
  // 创建数据库（如果不存在）
  await pool.query(`CREATE DATABASE IF NOT EXISTS chat 
    DEFAULT CHARACTER SET utf8mb4 
    DEFAULT COLLATE utf8mb4_unicode_ci`);

  // 使用数据库
  await pool.query('USE chat');

  // 创建迁移记录表
  await pool.query(`
    CREATE TABLE IF NOT EXISTS chat.migrations (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      name VARCHAR(255) NOT NULL,
      executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY uk_name (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库迁移记录表'
  `);

  // 创建用户会话表
  await pool.query(`
    CREATE TABLE IF NOT EXISTS chat.user_conversations (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
      conversation_id VARCHAR(64) NOT NULL COMMENT 'Coze会话ID',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      UNIQUE KEY uk_user_id (user_id),
      INDEX idx_conversation_id (conversation_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户对话映射表'
  `);
}

export async function down({ pool }) {
  // 删除表
  await pool.query('DROP TABLE IF EXISTS chat.user_conversations');
  await pool.query('DROP TABLE IF EXISTS chat.migrations');
} 
#!/bin/bash
set -e

# 使用sed替换SQL文件中的环境变量
cp /docker-entrypoint-initdb.d/01-init.sql /tmp/init.sql
sed -i "s/\${MYSQL_ROOT_PASSWORD}/$MYSQL_ROOT_PASSWORD/g" /tmp/init.sql

# 配置mysql命令不使用SSL
MYSQL_OPT=""
if mysql --help 2>&1 | grep -q ssl-mode; then
  # MySQL 客户端支持 ssl-mode 选项
  MYSQL_OPT="--ssl-mode=DISABLED"
elif mysql --help 2>&1 | grep -q ssl; then
  # MySQL 客户端支持 --ssl=0 选项
  MYSQL_OPT="--ssl=0"
else
  # 尝试 --skip-ssl 选项
  MYSQL_OPT="--skip-ssl"
fi

echo "使用MySQL连接选项: $MYSQL_OPT"

# 执行处理后的SQL文件
mysql -u root -p"$MYSQL_ROOT_PASSWORD" $MYSQL_OPT < /tmp/init.sql

# 清理临时文件
rm /tmp/init.sql

echo "数据库初始化完成，用户权限已设置！" 
import { pool } from '../connection.js';

export async function up({ pool }) {
  await pool.query(`
    CREATE TABLE IF NOT EXISTS chat.user_profiles (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
      company_name VA<PERSON>HAR(255) COMMENT '企业名称',
      contact_info VARCHAR(255) COMMENT '联系方式',
      business_details TEXT COMMENT '具体业务',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      UNIQUE KEY uk_user_id (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户档案信息表'
  `);
}

export async function down({ pool }) {
  await pool.query('DROP TABLE IF EXISTS chat.user_profiles');
} 
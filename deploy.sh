#!/bin/bash

# 构建Docker容器

echo "========== 开始部署Coze API集成系统 =========="

# 遇到错误立即退出
set -e

# 检查Docker是否已安装
if ! [ -x "$(command -v docker)" ]; then
  echo '错误: Docker没有安装.' >&2
  exit 1
fi

# 检查docker-compose是否已安装
if ! [ -x "$(command -v docker-compose)" ]; then
  echo '错误: docker-compose没有安装.' >&2
  exit 1
fi

# 确保.env文件存在
if [ ! -f .env ]; then
  echo "创建.env文件..."
  cp example.env .env
  # 生成随机MySQL密码
  MYSQL_PASSWORD=$(openssl rand -base64 12 | tr -dc 'a-zA-Z0-9')
  echo "MYSQL_ROOT_PASSWORD=$MYSQL_PASSWORD" >> .env
  echo "已生成随机MySQL密码，请修改.env文件设置其他必要参数后再次运行此脚本。"
  exit 0
fi

# 创建必要目录
echo "正在创建必要目录..."
mkdir -p logs
chmod 777 logs
mkdir -p db/init

# 停止并删除旧容器
echo "正在清理旧容器..."
docker-compose down || true

# 构建Docker镜像 (不使用缓存)
echo "当前正在构建 Docker 镜像 (不使用缓存)..."
docker-compose build --no-cache

# 使用 docker-compose 启动应用
echo "当前正在启动应用..."
docker-compose up -d

# 显示日志
echo "显示应用日志..."
docker-compose logs -f app

echo "应用已启动。"
echo "- API服务: http://localhost:8891"
echo "- 数据库管理: http://localhost:8080"
echo "======================================================="
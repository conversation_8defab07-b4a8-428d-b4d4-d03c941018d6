-- 初始化数据库和表（可选）
-- 这些SQL脚本只会在数据卷首次创建时执行

-- 确保使用正确的数据库
USE chat;

-- 确保root用户可以从任何主机连接
-- MySQL初始化脚本会自动替换${MYSQL_ROOT_PASSWORD}变量
CREATE USER IF NOT EXISTS 'root'@'%';
ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password BY '${MYSQL_ROOT_PASSWORD}';
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '${MYSQL_ROOT_PASSWORD}';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;

-- 可以在这里添加初始数据，但通常我们会使用Nodejs迁移工具来管理表结构 
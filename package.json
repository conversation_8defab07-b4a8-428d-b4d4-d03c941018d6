{"name": "coze-api-integration", "version": "1.0.0", "description": "扣子API集成示例，包含服务器代理和前端聊天组件", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "migrate": "node db/migrate.js", "migrate:up": "node db/migrate.js up", "migrate:down": "node db/migrate.js down", "migrate:create": "node db/migrate.js create", "migrate:status": "node db/migrate.js status"}, "keywords": ["coze", "api", "chatbot", "ai", "llm"], "author": "", "license": "MIT", "dependencies": {"@coze/api": "^1.2.1-beta.7", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mysql2": "^3.6.0", "umzug": "^3.5.1"}, "devDependencies": {"nodemon": "^3.0.1"}}
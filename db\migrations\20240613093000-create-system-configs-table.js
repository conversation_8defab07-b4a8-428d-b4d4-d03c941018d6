import { pool } from '../connection.js';
import dotenv from 'dotenv';
import { SystemConfigs } from '../models/SystemConfigs.js'; // 注意这里是相对路径

dotenv.config(); // 在迁移文件中加载环境变量

export async function up({ pool }) {
  // 创建系统配置表
  await pool.query(`
    CREATE TABLE IF NOT EXISTS chat.system_configs (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      config_key VARCHAR(255) NOT NULL UNIQUE COMMENT '配置项键名',
      config_value TEXT COMMENT '配置项值',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表'
  `);

  // 预置 COZE_API_TOKEN 和 COZE_BOT_ID
  const COZE_TOKEN = process.env.COZE_API_TOKEN;
  const BOT_ID = process.env.COZE_BOT_ID;

  if (COZE_TOKEN) {
    await SystemConfigs.setConfig('COZE_API_TOKEN', COZE_TOKEN);
    console.log('COZE_API_TOKEN 已预置到数据库。');
  } else {
    console.warn('警告: COZE_API_TOKEN 环境变量未设置，未预置到数据库。');
  }

  if (BOT_ID) {
    await SystemConfigs.setConfig('COZE_BOT_ID', BOT_ID);
    console.log('COZE_BOT_ID 已预置到数据库。');
  } else {
    console.warn('警告: COZE_BOT_ID 环境变量未设置，未预置到数据库。');
  }
}

export async function down({ pool }) {
  await pool.query('DROP TABLE IF EXISTS chat.system_configs');
} 
import { executeQuery } from '../connection.js';

export class SystemConfigs {
  /**
   * 根据配置项键名获取配置值
   * @param {string} key 配置项键名
   * @returns {Promise<string|null>} 配置值
   */
  static async getConfig(key) {
    const results = await executeQuery(
      'SELECT config_value FROM system_configs WHERE config_key = ?',
      [key]
    );
    return results.length > 0 ? results[0].config_value : null;
  }

  /**
   * 保存或更新配置项
   * @param {string} key 配置项键名
   * @param {string} value 配置项值
   * @returns {Promise<void>}
   */
  static async setConfig(key, value) {
    const existing = await executeQuery(
      'SELECT id FROM system_configs WHERE config_key = ?',
      [key]
    );

    if (existing.length > 0) {
      // 更新现有记录
      await executeQuery(
        'UPDATE system_configs SET config_value = ?, updated_at = CURRENT_TIMESTAMP WHERE config_key = ?',
        [value, key]
      );
    } else {
      // 插入新记录
      await executeQuery(
        'INSERT INTO system_configs (config_key, config_value) VALUES (?, ?)',
        [key, value]
      );
    }
  }
} 
import express from "express";
import bodyParser from "body-parser";
import { fileURLToPath } from "url";
import path from "path";
import cors from "cors";
import crypto from "crypto";
import fs from "fs";
import { cozeChatStream, getHistoryByUserId, filteredCozeChatStream } from './coze-api.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建日志目录
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir);
}

// 创建日志文件流
const logFile = fs.createWriteStream(path.join(logDir, 'app.log'), { flags: 'a' });

// 自定义日志函数
function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(message);
    logFile.write(logMessage);
}

const app = express();

// app.use(function (req, res, next) {
//   res.setHeader(
//     "Content-Security-Policy",
//     "frame-src 'self' https://intercom-sheets.com",
//   );
//   res.setHeader("X-Requested-With", "XMLHttpRequest");
//   next();
// });

app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

app.use(express.static("public"));
app.use(express.static(path.join(__dirname)));

const listener = app.listen(process.env.PORT || 8891, '0.0.0.0', () => {
  console.log("Your app is listening on port " + listener.address().port);
});


const initialCanvas = {
  canvas: {
    content: {
      components: [
        {
          "type": "text",
          "id": "ai-title",
          "text": "Customer Service",
          "style": "header"
        },
        {
          "type": "text",
          "id": "ai-desc",
          "text": "Available 24/7 to serve you.",
          "style": "paragraph"
        },
        {
          "type": "button",
          "label": "Get Started",
          "id": "ai-button",
          "action": {
            "type": "sheet",
            "url": "https://test124.abupdate.com/sheet"
          }
        }
      ],
    },
  },
};
// 重定向根路由到public/index.html
app.get("/", (request, response) => {
  // 设置缓存控制头
  response.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  response.setHeader('Pragma', 'no-cache');
  response.setHeader('Expires', '0');
  response.sendFile(path.join(__dirname, "public", "index.html"));
});

// Send the first canvas which will display a button
app.post("/initialize", (request, response) => {
  response.send(initialCanvas);
});

/*
When this endpoint is called, it will decode and verify the user, then display the sheet in the iFrame.
*/
app.post("/sheet", (req, res) => {
  log('=== /sheet 接口调用开始 ===');
  log('请求时间: ' + new Date().toISOString());
  log('请求头: ' + JSON.stringify(req.headers, null, 2));
  log('请求体: ' + JSON.stringify(req.body, null, 2));

  const filePath = path.join(__dirname, "public", "sheet.html");
  log('文件路径: ' + filePath);

  let user_id = '';
  try {
    if (req.body && req.body.intercom_data) {
      log('intercom_data 存在，开始解析');
      const jsonParsed = JSON.parse(req.body.intercom_data);
      log('解析后的 intercom_data: ' + JSON.stringify(jsonParsed, null, 2));
      const encodedUser = jsonParsed.user;
      log('encodedUser: ' + encodedUser);
      let decodedUser = decodeUser(encodedUser);
      log('解码后的用户信息: ' + decodedUser);
      const userObj = JSON.parse(decodedUser);
      user_id = userObj.user_id || '';
    } else {
      log('intercom_data 不存在或为空');
    }
  } catch (error) {
    log('处理过程中发生错误: ' + error.message);
    log('错误堆栈: ' + error.stack);
  }

  // 读取 sheet.html 并注入 user_id
  let html = fs.readFileSync(filePath, 'utf8');
  if (user_id) {
    html = html.replace('</head>', `<script>window.USER_ID = "${user_id}";</script></head>`);
  }

  // 设置缓存控制头
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.send(html);
});

/*
When this endpoint is called from within the sheet, it will:
- close the sheet
- gather the user-submitted data
- display the final canvas you would like to show the user

You could also take the user data and pass it from here to perform other actions.
*/
app.post("/submit-sheet", (req, res) => {
  // you can get data about the contact, company, and sheet from the request
  console.log(req.body);

  try {
    let displayDate = "selected date";
    
    if (req.body && req.body.sheet_values && req.body.sheet_values.date) {
      const chosenDate = new Date(req.body.sheet_values.date);
      // Extract the date part in YYYY-MM-DD format
      displayDate = chosenDate.toISOString().split("T")[0];
    }

    const finalCanvas = {
      canvas: {
        content: {
          components: [
            {
              type: "text",
              id: "closing",
              text: "Thanks! Your meeting is booked for " + displayDate,
              align: "center",
              style: "header",
            },
          ],
        },
      },
    };

    res.send(finalCanvas);
  } catch (error) {
    console.error("Error processing submit-sheet:", error);
    
    // 发送一个通用的回复
    const errorCanvas = {
      canvas: {
        content: {
          components: [
            {
              type: "text",
              id: "error",
              text: "Thank you for your submission!",
              align: "center",
              style: "header",
            },
          ],
        },
      },
    };
    
    res.send(errorCanvas);
  }
});

/*
This function can be used to decode the user object, which will allow you to verify the identity of the user.
*/
function decodeUser(encodedUser) {
  const secret_key = "844dffbe-60c7-4839-ba4c-3cf7cc6622f4";

  // base64 decoding
  const bData = Buffer.from(encodedUser, "base64");

  // convert data to buffers
  const ivlen = 12;
  const iv = bData.slice(0, ivlen);

  const taglen = 16;
  const tag = bData.slice(bData.length - taglen, bData.length);

  const cipherLen = bData.length - taglen;
  const cipherText = bData.slice(ivlen, cipherLen);

  let hash = crypto.createHash("sha256").update(secret_key);
  let key = Buffer.from(hash.digest("binary"), "binary"); //buffer from binary string.

  // AES 256 GCM Mode
  const decipher = crypto.createDecipheriv("aes-256-gcm", key, iv);
  decipher.setAuthTag(tag);

  // encrypt the given text
  let decrypted = decipher.update(cipherText, "binary", "utf8");
  decrypted += decipher.final("utf8");

  return decrypted;
}

// 新增 Coze 流式对话 SSE接口
app.get('/coze-chat/stream', async (req, res) => {
  const { user_id, message } = req.query;
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders && res.flushHeaders();
  res.write('retry: 3000\n'); // 建议客户端3秒重连
  try {
    // 使用过滤版本的流式响应
    for await (const chunk of filteredCozeChatStream(user_id, message)) {
      res.write(`event: message\ndata: ${JSON.stringify(chunk)}\n\n`);
    }
    res.write('event: done\ndata: [DONE]\n\n');
    res.end();
  } catch (err) {
    res.write(`event: error\ndata: ${JSON.stringify({ error: err.message })}\n\n`);
    res.end();
  }
});

// 获取用户对话历史API
app.get('/api/history/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: '用户ID不能为空'
      });
    }
    
    console.log(`接收到获取用户 ${userId} 对话历史请求`);
    
    // 调用API获取历史记录
    const result = await getHistoryByUserId(userId);
    
    // 返回结果
    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('处理历史记录请求失败:', error);
    res.status(500).json({
      success: false,
      error: `获取历史记录失败: ${error.message || '未知错误'}`
    });
  }
});

 
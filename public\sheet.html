<!doctype html>
<html>
  <head>
    <title>Customer Service</title>
    <!-- Adding the Messenger Sheet Library -->
    <script src="https://s3.amazonaws.com/intercom-sheets.com/messenger-sheet-library.latest.js"></script>
    <link rel="stylesheet" href="style.css" />
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
      // 配置marked库，减少嵌套标签
      document.addEventListener('DOMContentLoaded', function() {
        // 配置marked减少额外的嵌套标签
        marked.setOptions({
          headerIds: false,
          gfm: true,
          breaks: true
        });
      });
    </script>
    <style>
      body {
        font-family: 'TT Norms Pro', Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f5f8fa;
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      .chat-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
        margin: 0 auto;
      }
      
      .chat-header {
        background-color: #0087C8;
        color: white;
        padding: 15px;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      }
      
      .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        display: flex;
        flex-direction: column;
        /* 隐藏滚动条但保留滚动功能 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
      }
      
      /* Webkit浏览器(Chrome、Safari等)隐藏滚动条 */
      .chat-messages::-webkit-scrollbar {
        display: none;
      }
      
      .message {
        max-width: 80%;
        padding: 10px 15px;
        margin-bottom: 15px;
        border-radius: 18px;
        position: relative;
        word-wrap: break-word;
      }
      
      .message-row.bot {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        margin-bottom: 15px;
      }
      
      .message-row.user {
        display: flex;
        flex-direction: row-reverse;
        align-items: flex-start;
        margin-bottom: 15px;
      }
      
      .bot-avatar, .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 8px;
        flex-shrink: 0;
        background: #f4f6fa;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .bot-avatar img, .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .bot-message, .user-message {
        max-width: 55vw;
        padding: 12px 18px;
        border-radius: 18px;
        word-break: break-word;
        white-space: pre-wrap;
        font-size: 15px;
        line-height: 1.7;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        margin-bottom: 2px;
      }
      
      .bot-message {
        background: #f4f6fa;
        color: #222;
        border-bottom-left-radius: 5px;
      }
      
      .user-message {
        background: #0087C8;
        color: #fff;
        border-bottom-right-radius: 5px;
      }
      
      .bot-message a, .user-message a {
        color: #0087C8;
        text-decoration: underline;
      }
      
      .bot-message code, .user-message code {
        background: #f0f0f0;
        border-radius: 3px;
        padding: 2px 4px;
        font-size: 90%;
        color: #c7254e;
      }
      
      .bot-message pre, .user-message pre {
        background: #f0f0f0;
        border-radius: 5px;
        padding: 8px;
        overflow-x: auto;
        font-size: 14px;
        margin: 8px 0;
      }
      
      .bot-message blockquote, .user-message blockquote {
        border-left: 4px solid #e0e0e0;
        margin: 8px 0;
        padding: 4px 0 4px 12px;
        color: #888;
        background: none;
      }
      
      .bot-message ul, .user-message ul,
      .bot-message ol, .user-message ol {
        margin: 8px 0 8px 18px;
        padding: 0;
      }
      
      .bot-message hr, .user-message hr {
        border: none;
        border-top: 1px solid #e0e0e0;
        margin: 12px 0;
      }
      
      .input-container {
        display: flex;
        align-items: center;
        padding: 15px;
        background: #fff;
        border-top: 1px solid #e6e6e6;
        width: 100%;
        box-sizing: border-box;
      }
      
      .message-input {
        flex: 1;
        height: 40px;
        border: 1px solid #e6e6e6;
        border-radius: 20px;
        outline: none;
        font-size: 14px;
        padding: 0 16px;
      }
      
      .send-button {
        background: #0087C8;
        color: #fff;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        margin-left: 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .send-button:hover {
        background-color: #006ca0;
      }
      
      .typing-indicator {
        align-self: flex-start;
        background-color: #e5e5ea;
        padding: 10px 15px;
        border-radius: 18px;
        margin-bottom: 15px;
        display: none;
      }
      
      .typing-indicator span {
        height: 10px;
        width: 10px;
        float: left;
        margin: 0 1px;
        background-color: #9E9EA1;
        display: block;
        border-radius: 50%;
        opacity: 0.4;
      }
      
      .typing-indicator span:nth-of-type(1) {
        animation: 1s blink infinite 0.33s;
      }
      
      .typing-indicator span:nth-of-type(2) {
        animation: 1s blink infinite 0.66s;
      }
      
      .typing-indicator span:nth-of-type(3) {
        animation: 1s blink infinite 0.99s;
      }
      
      @keyframes blink {
        50% {
          opacity: 1;
        }
      }
      
      .quick-replies {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        margin-bottom: 10px;
      }
      
      .quick-reply {
        background-color: white;
        border: 1px solid #0087C8;
        color: #0087C8;
        border-radius: 18px;
        padding: 8px 16px;
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: background-color 0.3s;
      }
      
      .quick-reply:hover {
        background-color: #e6f7ff;
      }
      
      .typing-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 2px;
        background: #9E9EA1;
        border-radius: 50%;
        opacity: 0.4;
        animation: blink 1.4s infinite both;
      }
      .typing-dot:nth-child(2) { animation-delay: 0.2s; }
      .typing-dot:nth-child(3) { animation-delay: 0.4s; }
      @keyframes blink {
        0%, 80%, 100% { opacity: 0.4; }
        40% { opacity: 1; }
      }
    </style>
  </head>

  <body>
    <div class="chat-container">
      <div class="chat-messages" id="chat-messages" style="min-height: 200px; width: 100%; box-sizing: border-box;"></div>
      <div class="typing-indicator" id="typing-indicator">
        <span></span>
        <span></span>
        <span></span>
      </div>
      <div class="input-container">
        <input type="text" class="message-input" id="message-input" placeholder="Please enter your question...">
        <button class="send-button" id="send-button">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 2L11 13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>

    <script>
      // 获取 user_id
      const userId = window.USER_ID;
      const chatMessages = document.getElementById('chat-messages');
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');
      const typingIndicator = document.getElementById('typing-indicator');

      // 页面加载时获取历史对话
      document.addEventListener('DOMContentLoaded', function() {
        if (userId) {
          fetchChatHistory();
        }
      });

      // 获取聊天历史记录
      async function fetchChatHistory() {
        try {
          const response = await fetch(`/api/history/${encodeURIComponent(userId)}`);
          const data = await response.json();
          
          if (data.success && data.data && data.data.messages && data.data.messages.length > 0) {
            renderChatHistory(data.data.messages);
          }
        } catch (error) {
          console.error('获取历史记录失败:', error);
        }
      }

      // 渲染历史对话记录
      function renderChatHistory(messages) {
        // 清空现有的消息
        chatMessages.innerHTML = '';
        
        // 按时间顺序渲染消息
        messages.forEach(message => {
          // 跳过不需要渲染的系统消息
          if (message.content && typeof message.content === 'string') {
            // 检查是否是不需要渲染的系统消息
            if (message.content.includes('"msg_type":"generate_answer_finish"')) {
              return;
            }
            
            // 检查消息是否是JSON格式的MDM对话支持信息
            if (message.content.startsWith('{') && message.content.endsWith('}')) {
              try {
                const msgObj = JSON.parse(message.content);
                // 判断是否是MDM对话支持的JSON格式
                if (msgObj.name && msgObj.name.includes('mdm_conversational_support') && 
                    msgObj.plugin_id && msgObj.plugin_name) {
                  // 这是一个需要显示在用户侧的系统消息，无论是否是assistant角色
                  addUserMessage(message.content);
                  return;
                }
              } catch (e) {
                // 解析失败，当作普通消息处理
              }
            }
          }
          
          if (message.role === 'user') {
            addUserMessage(message.content);
          } else if (message.role === 'assistant') {
            addBotMessage(message.content);
          }
        });
        
        // 滚动到底部
        scrollToBottom();
      }

      sendButton.addEventListener('click', sendMessage);
      messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') sendMessage();
      });

      function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;
        
        // 检查消息是否是MDM对话支持的JSON格式
        let isMDMJsonMessage = false;
        let displayText = message;
        
        if (message.startsWith('{') && message.endsWith('}')) {
          try {
            const msgObj = JSON.parse(message);
            if (msgObj.name && msgObj.name.includes('mdm_conversational_support') && 
                msgObj.plugin_id && msgObj.plugin_name) {
              // 这是一个需要显示在用户侧的MDM系统消息
              isMDMJsonMessage = true;
              
              // 如果有input字段，则只显示input内容
              if (msgObj.arguments && msgObj.arguments.input) {
                displayText = msgObj.arguments.input;
              }
            }
          } catch (e) {
            // 解析失败，当作普通消息处理
          }
        }
        
        // 添加用户消息并清空输入框
        addUserMessage(message); // 原样传入，让addUserMessage函数处理显示逻辑
        messageInput.value = '';
        
        // 如果是MDM消息，则不需要AI响应
        if (isMDMJsonMessage) {
          return;
        }
        
        // 先插入AI气泡，内容为...动画
        const botMsgDiv = addBotMessage('', true);
        const eventSource = new EventSource(`/coze-chat/stream?user_id=${encodeURIComponent(userId)}&message=${encodeURIComponent(message)}`);
        eventSource.addEventListener('message', function(event) {
          try {
            const data = event.data;
            // 首先检查是否是纯字符串JSON
            let chunk;
            try {
              chunk = JSON.parse(data);
            } catch (e) {
              // 不是有效的JSON，可能是普通文本
              if (botMsgDiv) {
                // 直接设置内容，不使用marked，避免额外嵌套
                botMsgDiv.textContent = data;
              }
              scrollToBottom();
              return;
            }
            
            // 处理新格式的过滤内容流
            if (chunk.event === 'content.update' && chunk.data && chunk.data.content) {
              // 增量更新内容
              if (botMsgDiv) {
                const currentContent = botMsgDiv.innerHTML || '';
                
                // 去掉开头的换行符
                let newContent = chunk.data.content;
                if (currentContent.includes('typing-dot')) {
                  // 如果是第一次更新内容（替换typing动画），删除开头的换行符
                  newContent = newContent.replace(/^\n+/, '');
                }
                
                // 如果当前内容是打字动画，则替换掉
                if (currentContent.includes('typing-dot')) {
                  // 优先使用textContent避免嵌套，对于需要格式化的内容使用innerHTML
                  if (newContent.includes('`') || newContent.includes('*')) {
                    botMsgDiv.innerHTML = marked.parse(newContent);
                  } else {
                    botMsgDiv.textContent = newContent;
                  }
                } else {
                  // 追加新内容
                  if (newContent.includes('`') || newContent.includes('*')) {
                    botMsgDiv.innerHTML = marked.parse(currentContent + newContent);
                  } else {
                    // 纯文本内容直接添加，避免嵌套
                    botMsgDiv.textContent = (botMsgDiv.textContent || '') + newContent;
                  }
                }
                scrollToBottom();
              }
            } 
            else if (chunk.event === 'content.final' && chunk.data && chunk.data.content) {
              // 最终完整内容
              if (botMsgDiv) {
                // 去掉开头的换行符
                let finalContent = chunk.data.content.replace(/^\n+/, '');
                
                // 优先使用textContent避免嵌套，对于需要格式化的内容使用innerHTML
                if (finalContent.includes('`') || finalContent.includes('*')) {
                  botMsgDiv.innerHTML = marked.parse(finalContent);
                } else {
                  botMsgDiv.textContent = finalContent;
                }
                scrollToBottom();
              }
            }
            // 向后兼容：处理原始消息格式（未过滤的情况）
            else if (chunk.data && chunk.data.type === 'function_call' || chunk.data && chunk.data.type === 'tool_response') {
              // 跳过function_call和tool_response类型的消息
              console.log("跳过消息类型:", chunk.data.type);
              return;
            }
            else if (chunk.data && chunk.data.type === 'answer' && chunk.data.content) {
              // 直接覆盖气泡内容，不累加
              if (botMsgDiv) {
                // 去掉开头的换行符
                let answerContent = chunk.data.content.replace(/^\n+/, '');
                
                // 优先使用textContent避免嵌套，对于需要格式化的内容使用innerHTML
                if (answerContent.includes('`') || answerContent.includes('*')) {
                  botMsgDiv.innerHTML = marked.parse(answerContent);
                } else {
                  botMsgDiv.textContent = answerContent;
                }
              }
              scrollToBottom();
            }
            // 跳过verbose类型消息
            else if (chunk.data && chunk.data.type === 'verbose') {
              console.log("跳过verbose类型消息");
              return;
            }
          } catch (e) {
            console.error('处理消息时出错:', e);
          }
        });
        
        eventSource.addEventListener('done', function() {
          eventSource.close();
        });
        
        eventSource.addEventListener('error', function(event) {
          console.error('EventSource错误:', event);
          if (botMsgDiv) {
            botMsgDiv.textContent = '连接出错，请重试。';
          }
          eventSource.close();
        });
      }

      function addBotMessage(text, isTyping = false) {
        // 检查是否是需要过滤的系统消息
        if (typeof text === 'string' && text.includes('"msg_type":"generate_answer_finish"')) {
          return null; // 不显示这类消息
        }
        
        // 检查是否是JSON格式的MDM对话支持信息，这类消息应该显示在用户侧
        if (typeof text === 'string' && text.startsWith('{') && text.endsWith('}')) {
          try {
            const msgObj = JSON.parse(text);
            if (msgObj.name && msgObj.name.includes('mdm_conversational_support') && 
                msgObj.plugin_id && msgObj.plugin_name) {
              // 这类消息应显示在用户侧，而不是机器人侧
              return addUserMessage(text);
            }
          } catch (e) {
            // 解析失败，按正常逻辑处理
          }
        }
        
        // 去掉文本开头的换行符
        if (typeof text === 'string') {
          text = text.replace(/^\n+/, ''); // 移除开头的所有换行符
        }
        
        const messageRow = document.createElement('div');
        messageRow.className = 'message-row bot';
        
        const botAvatar = document.createElement('div');
        botAvatar.className = 'bot-avatar';
        botAvatar.innerHTML = '<img src="bot.png" alt="AI">';
        
        const message = document.createElement('div');
        message.className = 'message bot-message';
        
        if (isTyping) {
          message.innerHTML = '<span class="typing-dot"></span><span class="typing-dot"></span><span class="typing-dot"></span>';
        } else {
          // 直接处理内容，区分纯文本和需要Markdown解析的内容
          try {
            // 检查是否包含需要markdown解析的特殊字符
            if (text.includes('`') || text.includes('*') || text.includes('#') || 
                text.includes('>') || text.includes('-') || text.includes('[')) {
              // 包含Markdown语法，使用marked解析
              message.innerHTML = marked.parse(text);
            } else {
              // 纯文本内容，直接使用textContent避免额外嵌套
              message.textContent = text;
            }
          } catch (e) {
            // 出错时使用文本内容
            message.textContent = text;
          }
        }
        
        messageRow.appendChild(botAvatar);
        messageRow.appendChild(message);
        chatMessages.appendChild(messageRow);
        scrollToBottom();
        return message;
      }

      function addUserMessage(text) {
        // 如果是JSON格式的消息（包含plugin_id, plugin_name等关键字），也要显示在用户这边
        // 例如：{"name":"ts-mdm_conversational_support-mdm_conversational_support","arguments":{"input":"如何注册"},...}
        let displayText = text;
        
        // 尝试解析JSON格式的MDM消息，只显示input字段
        if (typeof text === 'string' && text.startsWith('{') && text.endsWith('}')) {
          try {
            const msgObj = JSON.parse(text);
            if (msgObj.name && msgObj.name.includes('mdm_conversational_support') && 
                msgObj.plugin_id && msgObj.plugin_name && 
                msgObj.arguments && msgObj.arguments.input) {
              // 只显示input字段内容
              displayText = msgObj.arguments.input;
            }
          } catch (e) {
            // 解析失败，使用原始文本
            console.error('解析JSON消息失败:', e);
          }
        }
        
        const messageRow = document.createElement('div');
        messageRow.className = 'message-row user';
        
        const userAvatar = document.createElement('div');
        userAvatar.className = 'user-avatar';
        userAvatar.innerHTML = '<img src="user.png" alt="Me">';
        
        const message = document.createElement('div');
        message.className = 'message user-message';
        message.textContent = displayText;
        
        messageRow.appendChild(userAvatar);
        messageRow.appendChild(message);
        chatMessages.appendChild(messageRow);
        scrollToBottom();
        return message;
      }

      function showTypingIndicator() {
        typingIndicator.style.display = 'block';
        scrollToBottom();
      }
      function hideTypingIndicator() {
        typingIndicator.style.display = 'none';
      }
      function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
      // 保留原有关闭事件和数据提交逻辑
      function submitSheetData() {
        const messages = Array.from(chatMessages.querySelectorAll('.message')).map(msg => {
          return {
            type: msg.classList.contains('user-message') ? 'user' : 'bot',
            text: msg.textContent
          };
        });
        INTERCOM_MESSENGER_SHEET_LIBRARY.submitSheet({
          conversation: messages,
          completed: true
        });
      }
      document.querySelector('.chat-header').addEventListener('click', function() {
        submitSheetData();
      });
    </script>
  </body>
</html> 
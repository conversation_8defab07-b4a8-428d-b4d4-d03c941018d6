import { Umzug, memoryStorage } from 'umzug';
import { pool } from '../connection.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 保证 migrations 表存在
async function ensureMigrationsTable() {
  await pool.query(`
    CREATE TABLE IF NOT EXISTS migrations (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      name VARCHAR(255) NOT NULL,
      executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY uk_name (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库迁移记录表'
  `);
}

// 手动创建迁移列表
async function getCustomMigrations() {
  try {
    const migrationDir = __dirname;
    console.log('迁移目录:', migrationDir);
    
    // 读取目录中的所有文件
    const files = await fs.readdir(migrationDir);
    
    // 过滤迁移文件
    const migrationFiles = files.filter(
      file => file.match(/^\d+.*\.js$/) && file !== 'config.js'
    );
    
    console.log('找到迁移文件:', migrationFiles);
    
    // 创建迁移对象
    const migrations = [];
    
    for (const file of migrationFiles) {
      try {
        const name = file;
        const fullPath = join(migrationDir, file);
        console.log(`处理迁移文件: ${name}, 路径: ${fullPath}`);
        
        // 正确格式化Windows路径为URL格式
        const fileUrl = `file:///${fullPath.replace(/\\/g, '/').replace(/^\//, '')}`;
        console.log(`导入URL: ${fileUrl}`);
        
        const module = await import(fileUrl);
        console.log(`成功导入迁移 ${name}, 导出内容:`, Object.keys(module));
        
        if (!module.up || typeof module.up !== 'function') {
          throw new Error(`迁移文件 ${name} 没有导出 up 函数`);
        }
        
        migrations.push({
          name,
          up: async () => {
            console.log(`执行迁移 ${name} 的 up 方法`);
            return module.up({ pool });
          },
          down: async () => {
            console.log(`执行迁移 ${name} 的 down 方法`);
            return module.down({ pool });
          }
        });
      } catch (err) {
        console.error(`处理迁移文件 ${file} 失败:`, err);
        throw err;
      }
    }
    
    return migrations;
  } catch (err) {
    console.error('获取迁移文件列表失败:', err);
    throw err;
  }
}

// 创建迁移配置
export const umzug = new Umzug({
  migrations: await getCustomMigrations(),
  context: { pool },
  storage: {
    async executed() {
      try {
        await ensureMigrationsTable();
        const [rows] = await pool.query('SELECT name FROM migrations');
        console.log('已执行的迁移记录:', rows);
        return rows.map(row => row.name);
      } catch (err) {
        console.error('获取已执行迁移失败:', err);
        return [];
      }
    },
    async logMigration({ name }) {
      try {
        await ensureMigrationsTable();
        console.log(`记录迁移 ${name}`);
        await pool.query('INSERT INTO migrations (name) VALUES (?)', [name]);
      } catch (err) {
        console.error(`记录迁移 ${name} 失败:`, err);
        throw err;
      }
    },
    async unlogMigration({ name }) {
      try {
        await ensureMigrationsTable();
        console.log(`删除迁移记录 ${name}`);
        await pool.query('DELETE FROM migrations WHERE name = ?', [name]);
      } catch (err) {
        console.error(`删除迁移记录 ${name} 失败:`, err);
        throw err;
      }
    }
  }
}); 
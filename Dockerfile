FROM node:22-alpine

# 安装mysql客户端和其他工具
RUN apk add --no-cache bash mysql-client curl bind-tools iputils

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制应用代码
COPY . .

# 确保脚本使用Unix风格的行结束符
RUN if [ -f docker-entrypoint.sh ]; then \
    sed -i 's/\r$//' docker-entrypoint.sh; \
    chmod +x docker-entrypoint.sh; \
    fi

# 对外暴露端口
EXPOSE 8891

# 使用启动脚本
ENTRYPOINT ["/app/docker-entrypoint.sh"]
import { executeQuery } from '../connection.js';

export class UserProfiles {
  /**
   * 根据用户ID获取用户档案信息
   * @param {string} userId 用户ID
   * @returns {Promise<{companyName: string|null, contactInfo: string|null, businessDetails: string|null}|null>} 用户档案信息
   */
  static async getProfile(userId) {
    const results = await executeQuery(
      'SELECT company_name, contact_info, business_details FROM user_profiles WHERE user_id = ?',
      [userId]
    );
    return results.length > 0 ? {
      companyName: results[0].company_name,
      contactInfo: results[0].contact_info,
      businessDetails: results[0].business_details
    } : null;
  }

  /**
   * 保存或更新用户档案信息
   * @param {string} userId 用户ID
   * @param {string|null} companyName 企业名称
   * @param {string|null} contactInfo 联系方式
   * @param {string|null} businessDetails 具体业务
   * @returns {Promise<void>}
   */
  static async saveProfile(userId, companyName = null, contactInfo = null, businessDetails = null) {
    // 检查是否已存在记录
    const existing = await executeQuery(
      'SELECT id FROM user_profiles WHERE user_id = ?',
      [userId]
    );

    if (existing.length > 0) {
      // 更新现有记录
      await executeQuery(
        'UPDATE user_profiles SET company_name = ?, contact_info = ?, business_details = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
        [companyName, contactInfo, businessDetails, userId]
      );
    } else {
      // 插入新记录
      await executeQuery(
        'INSERT INTO user_profiles (user_id, company_name, contact_info, business_details) VALUES (?, ?, ?, ?)',
        [userId, companyName, contactInfo, businessDetails]
      );
    }
  }

  /**
   * 更新用户的联系方式
   * @param {string} userId 用户ID
   * @param {string|null} contactInfo 联系方式
   * @returns {Promise<void>}
   */
  static async updateContactInfo(userId, contactInfo) {
    const existing = await executeQuery(
      'SELECT id FROM user_profiles WHERE user_id = ?',
      [userId]
    );

    if (existing.length > 0) {
      await executeQuery(
        'UPDATE user_profiles SET contact_info = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
        [contactInfo, userId]
      );
    } else {
      // 如果记录不存在，则插入新记录
      await executeQuery(
        'INSERT INTO user_profiles (user_id, contact_info) VALUES (?, ?)',
        [userId, contactInfo]
      );
    }
  }

  /**
   * 更新用户的企业名称
   * @param {string} userId 用户ID
   * @param {string|null} companyName 企业名称
   * @returns {Promise<void>}
   */
  static async updateCompanyName(userId, companyName) {
    const existing = await executeQuery(
      'SELECT id FROM user_profiles WHERE user_id = ?',
      [userId]
    );

    if (existing.length > 0) {
      await executeQuery(
        'UPDATE user_profiles SET company_name = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
        [companyName, userId]
      );
    } else {
      // 如果记录不存在，则插入新记录
      await executeQuery(
        'INSERT INTO user_profiles (user_id, company_name) VALUES (?, ?)',
        [userId, companyName]
      );
    }
  }

  /**
   * 更新用户的具体业务
   * @param {string} userId 用户ID
   * @param {string|null} businessDetails 具体业务
   * @returns {Promise<void>}
   */
  static async updateBusinessDetails(userId, businessDetails) {
    const existing = await executeQuery(
      'SELECT id FROM user_profiles WHERE user_id = ?',
      [userId]
    );

    if (existing.length > 0) {
      await executeQuery(
        'UPDATE user_profiles SET business_details = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
        [businessDetails, userId]
      );
    } else {
      // 如果记录不存在，则插入新记录
      await executeQuery(
        'INSERT INTO user_profiles (user_id, business_details) VALUES (?, ?)',
        [userId, businessDetails]
      );
    }
  }

} 
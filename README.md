# Intercom Meeting Scheduler

这是一个使用Intercom Canvas Kit和Sheet功能的会议预约应用程序。应用程序允许用户通过Intercom聊天界面选择会议日期。

## 功能

- 在Intercom聊天窗口中显示"Ai客服"按钮
- 点击按钮打开一个预约表单sheet
- 用户可以选择会议日期
- 提交后显示确认消息

## 项目结构

```
├── index.js              # 主服务器文件
├── public/               # 静态文件
│   ├── index.html        # 主HTML页面
│   ├── sheet.html        # 预约表单
│   └── style.css         # 样式表
├── style.css             # 根目录样式表
├── package.json          # 项目依赖
├── Dockerfile            # Docker构建文件
└── docker-compose.yml    # Docker编排文件
```

## 安装说明

### 本地开发

1. 克隆此仓库
2. 安装依赖:

```bash
npm install
```

3. 启动应用:

```bash
npm start
```

4. 在浏览器中访问 `http://localhost:8891`

### Docker部署

1. 使用Docker构建镜像:

```bash
docker build -t intercom-meeting-scheduler .
```

2. 运行Docker容器:

```bash
docker run -p 8891:8891 -d intercom-meeting-scheduler
```

或者使用docker-compose:

```bash
docker-compose up -d
```

应用将在 `http://localhost:8891` 上运行。

## 开发模式

使用以下命令启动开发服务器（自动重新加载）:

```bash
npm run dev
```

## Intercom配置

要使用此应用程序:

1. 在Intercom Dashboard中创建一个新的应用
2. 在public/index.html中更新APP_ID
3. 在index.js中更新initialCanvas的sheet URL为您的实际部署URL
4. 确保sheet.html正确配置为处理日期选择 
import { umzug } from './migrations/config.js';
import { closePool } from './connection.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { writeFile, mkdir, readdir } from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function main() {
  try {
    // 调试：输出当前迁移目录下的文件
    const migrationsDir = join(__dirname, 'migrations');
    const files = await readdir(migrationsDir);
    console.log('当前迁移目录文件:', files);
    // 调试：输出umzug pending和executed迁移
    const pending = await umzug.pending();
    const executed = await umzug.executed();
    console.log('umzug pending:', pending.map(m => m.name));
    console.log('umzug executed:', executed.map(m => m.name));

    const command = process.argv[2];
    const name = process.argv[3];

    switch (command) {
      case 'up':
        // 执行所有未执行的迁移
        await umzug.up();
        console.log('所有迁移已执行完成');
        break;

      case 'down':
        // 回滚最后一次迁移
        await umzug.down();
        console.log('最后一次迁移已回滚');
        break;

      case 'create':
        // 创建新的迁移文件
        if (!name) {
          throw new Error('请提供迁移名称');
        }
        const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
        const fileName = `${timestamp}-${name}.js`;
        const migrationsDir = join(__dirname, 'migrations');
        const filePath = join(migrationsDir, fileName);
        
        // 确保 migrations 目录存在
        await mkdir(migrationsDir, { recursive: true });
        
        // 创建迁移文件模板
        const template = `import { pool } from '../connection.js';

export async function up({ pool }) {
  // TODO: 实现迁移逻辑
  await pool.query(\`
    // 在这里编写创建表或修改表的SQL
  \`);
}

export async function down({ pool }) {
  // TODO: 实现回滚逻辑
  await pool.query(\`
    // 在这里编写回滚的SQL
  \`);
}
`;
        
        await writeFile(filePath, template);
        console.log(`迁移文件已创建: ${fileName}`);
        break;

      case 'status':
        // 显示迁移状态
        console.log('已执行的迁移:');
        executed.forEach(m => console.log(`✓ ${m.name}`));
        console.log('\n待执行的迁移:');
        pending.forEach(m => console.log(`○ ${m.name}`));
        break;

      default:
        console.log(`
使用方法:
  node migrate.js <command> [name]

命令:
  up      - 执行所有未执行的迁移
  down    - 回滚最后一次迁移
  create  - 创建新的迁移文件
  status  - 显示迁移状态

示例:
  node migrate.js up
  node migrate.js down
  node migrate.js create add_user_table
  node migrate.js status
        `);
    }
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  } finally {
    await closePool();
  }
}

main(); 
#!/bin/bash
set -e

# 显示环境变量（密码隐藏）
echo "=== 数据库连接参数 ==="
echo "数据库主机: $DB_HOST"
echo "数据库端口: $DB_PORT"
echo "数据库名称: $DB_NAME"
echo "数据库用户: $DB_USER"
echo "数据库密码: [已隐藏，长度: ${#DB_PASSWORD}]"
echo "========================"

# 等待MySQL启动...
echo "等待MySQL启动..."
max_retry=60  # 增加重试次数
counter=0

# 先等待网络稳定
sleep 10

# 配置mysql命令不使用SSL
MYSQL_OPT=""
if mysql --help 2>&1 | grep -q ssl-mode; then
  # MySQL 客户端支持 ssl-mode 选项
  MYSQL_OPT="--ssl-mode=DISABLED"
elif mysql --help 2>&1 | grep -q ssl; then
  # MySQL 客户端支持 --ssl=0 选项
  MYSQL_OPT="--ssl=0"
else
  # 尝试 --skip-ssl 选项
  MYSQL_OPT="--skip-ssl"
fi

echo "使用MySQL连接选项: $MYSQL_OPT"

while [ $counter -lt $max_retry ]
do
  echo "尝试连接MySQL... ($counter/$max_retry)"
  
  # 尝试多种禁用SSL的方式
  if MYSQL_PWD="$DB_PASSWORD" mysql -h "$DB_HOST" -u "$DB_USER" $MYSQL_OPT --connect-timeout=5 -e "SELECT 1" > /dev/null 2>&1; then
    echo "MySQL服务已就绪，连接成功!"
    break
  else
    counter=$((counter+1))
    echo "等待MySQL...尝试 $counter/$max_retry"
    
    # 显示更多诊断信息
    if [ $((counter % 10)) -eq 0 ]; then
      echo "===== 诊断信息 ====="
      echo "检查网络连接..."
      ping -c 1 $DB_HOST || echo "无法ping通MySQL主机"
      
      echo "检查主机是否可访问..."
      getent hosts $DB_HOST || echo "无法解析MySQL主机名"
      
      echo "尝试各种连接参数..."
      echo "命令: mysql -h $DB_HOST -u $DB_USER $MYSQL_OPT -e \"SELECT 1\""
      MYSQL_PWD="$DB_PASSWORD" mysql -h "$DB_HOST" -u "$DB_USER" $MYSQL_OPT -e "SELECT 1" || echo "错误代码: $?"
      
      # 尝试不同的SSL选项
      for opt in "--skip-ssl" "--ssl=0" "--ssl-mode=DISABLED" ""; do
        echo "尝试选项: $opt"
        MYSQL_PWD="$DB_PASSWORD" mysql -h "$DB_HOST" -u "$DB_USER" $opt -e "SELECT 1" 2>/dev/null && echo "成功!" && break
      done
      
      echo "===================="
    fi
    
    sleep 2
  fi
done

if [ $counter -eq $max_retry ]; then
  echo "无法连接到MySQL，请检查数据库连接配置"
  echo "最终诊断信息:"
  echo "1. 密码长度: ${#DB_PASSWORD}"
  echo "2. MySQL主机: $DB_HOST"
  echo "3. 连接选项: $MYSQL_OPT"
  MYSQL_PWD="$DB_PASSWORD" mysql -h "$DB_HOST" -u "$DB_USER" $MYSQL_OPT -e "SELECT 1" || echo "连接失败，错误: $?"
  exit 1
fi

# 执行数据库迁移
echo "执行数据库迁移..."
npm run migrate:up

# 启动应用
echo "启动应用..."
exec npm start 